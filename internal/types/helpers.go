// Package types provides type conversion and safety helpers
// to ensure consistent value/pointer semantics across the codebase
package types

import (
	"database/sql"
	"math"
)

// SafeInt32 safely converts int to int32 with overflow protection
// Returns math.MaxInt32 if the input exceeds the int32 range
func SafeInt32(i int) int32 {
	if i > math.MaxInt32 {
		return math.MaxInt32
	}
	if i < math.MinInt32 {
		return math.MinInt32
	}
	return int32(i)
}

// SafeInt safely converts int32 to int
// No overflow possible since int is at least 32 bits
func SafeInt(i int32) int {
	return int(i)
}

// Int32Ptr returns a pointer to the int32 value
func Int32Ptr(i int32) *int32 {
	return &i
}

// IntPtr returns a pointer to the int value
func IntPtr(i int) *int {
	return &i
}

// StringPtr returns a pointer to the string value
func StringPtr(s string) *string {
	return &s
}

// BoolPtr returns a pointer to the bool value
func BoolPtr(b bool) *bool {
	return &b
}

// NullStringFromPtr creates sql.NullString from string pointer
// Returns invalid NullString if pointer is nil
func NullStringFromPtr(s *string) sql.NullString {
	if s == nil {
		return sql.NullString{Valid: false}
	}
	return sql.NullString{String: *s, Valid: true}
}

// NullString creates sql.NullString from string value
// Returns invalid NullString if string is empty
func NullString(s string) sql.NullString {
	if s == "" {
		return sql.NullString{Valid: false}
	}
	return sql.NullString{String: s, Valid: true}
}

// NullInt32FromPtr creates sql.NullInt32 from int32 pointer
func NullInt32FromPtr(i *int32) sql.NullInt32 {
	if i == nil {
		return sql.NullInt32{Valid: false}
	}
	return sql.NullInt32{Int32: *i, Valid: true}
}

// NullInt32 creates sql.NullInt32 from int32 value
func NullInt32(i int32) sql.NullInt32 {
	return sql.NullInt32{Int32: i, Valid: true}
}

// StringPtrFromNull extracts string pointer from sql.NullString
// Returns nil if NullString is invalid
func StringPtrFromNull(ns sql.NullString) *string {
	if !ns.Valid {
		return nil
	}
	return &ns.String
}

// Int32PtrFromNull extracts int32 pointer from sql.NullInt32
// Returns nil if NullInt32 is invalid
func Int32PtrFromNull(ni sql.NullInt32) *int32 {
	if !ni.Valid {
		return nil
	}
	return &ni.Int32
}

// PaginationParams represents standardized pagination parameters
type PaginationParams struct {
	Page     int32 // Use int32 to match database ID types
	PageSize int32 // Use int32 to match database limit types
	Offset   int32 // Calculated offset for database queries
}

// NewPaginationParams creates validated pagination parameters
func NewPaginationParams(page, pageSize int) PaginationParams {
	// Validate and set defaults
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 20 // Default page size
	}
	if pageSize > 100 {
		pageSize = 100 // Maximum page size
	}

	// Calculate offset safely
	offset := (page - 1) * pageSize

	return PaginationParams{
		Page:     SafeInt32(page),
		PageSize: SafeInt32(pageSize),
		Offset:   SafeInt32(offset),
	}
}

// PaginatedResponse represents a standardized paginated response
type PaginatedResponse[T any] struct {
	Items      []T   `json:"items"`
	TotalCount int64 `json:"total_count"`
	Page       int32 `json:"page"`
	PageSize   int32 `json:"page_size"`
	TotalPages int32 `json:"total_pages"`
}

// NewPaginatedResponse creates a paginated response with calculated total pages
func NewPaginatedResponse[T any](items []T, totalCount int64, params PaginationParams) PaginatedResponse[T] {
	totalPages := int32(0)
	if totalCount > 0 && params.PageSize > 0 {
		totalPages = SafeInt32(int((totalCount + int64(params.PageSize) - 1) / int64(params.PageSize)))
	}

	return PaginatedResponse[T]{
		Items:      items,
		TotalCount: totalCount,
		Page:       params.Page,
		PageSize:   params.PageSize,
		TotalPages: totalPages,
	}
}

// ValueOrDefault returns the value if pointer is not nil, otherwise returns default
func ValueOrDefault[T any](ptr *T, defaultVal T) T {
	if ptr == nil {
		return defaultVal
	}
	return *ptr
}

// IsNilOrEmpty checks if string pointer is nil or points to empty string
func IsNilOrEmpty(s *string) bool {
	return s == nil || *s == ""
}

// IsValidID checks if an ID is valid (> 0)
func IsValidID(id int32) bool {
	return id > 0
}

// CoalesceString returns the first non-empty string from the arguments
func CoalesceString(strings ...string) string {
	for _, s := range strings {
		if s != "" {
			return s
		}
	}
	return ""
}

// CoalesceStringPtr returns the first non-nil, non-empty string pointer
func CoalesceStringPtr(ptrs ...*string) *string {
	for _, ptr := range ptrs {
		if !IsNilOrEmpty(ptr) {
			return ptr
		}
	}
	return nil
}